import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React, { useState } from 'react';
import {
  Alert,
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
  ScrollView,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import { tabunganStyles } from './styles';

export default function TambahSetoranScreen() {
  const [amount, setAmount] = useState('');
  const [date, setDate] = useState(new Date().toISOString().split('T')[0]);
  const [note, setNote] = useState('');
  const [loading, setLoading] = useState(false);
  const [focusedInput, setFocusedInput] = useState<string | null>(null);

  const formatCurrency = (value: string) => {
    // Remove non-numeric characters
    const numericValue = value.replace(/[^0-9]/g, '');
    
    // Format with thousand separators
    if (numericValue) {
      return new Intl.NumberFormat('id-ID').format(parseInt(numericValue));
    }
    return '';
  };

  const handleAmountChange = (text: string) => {
    const formatted = formatCurrency(text);
    setAmount(formatted);
  };

  const getNumericAmount = () => {
    return parseInt(amount.replace(/[^0-9]/g, '')) || 0;
  };

  const handleSubmit = async () => {
    const numericAmount = getNumericAmount();
    
    if (!numericAmount || numericAmount <= 0) {
      Alert.alert('Kesalahan', 'Harap masukkan jumlah setoran yang valid');
      return;
    }

    if (!date) {
      Alert.alert('Kesalahan', 'Harap pilih tanggal setoran');
      return;
    }

    setLoading(true);

    try {
      // TODO: Save to database
      // For now, just simulate saving
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      Alert.alert(
        'Berhasil!',
        `Setoran sebesar Rp ${amount} berhasil dicatat`,
        [
          {
            text: 'OK',
            onPress: () => {
              // Reset form
              setAmount('');
              setNote('');
              setDate(new Date().toISOString().split('T')[0]);
              
              // Navigate back or to history
              router.back();
            },
          },
        ]
      );
    } catch (error) {
      Alert.alert('Kesalahan', 'Gagal menyimpan setoran. Silakan coba lagi.');
    } finally {
      setLoading(false);
    }
  };

  const formatDisplayCurrency = (numericAmount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(numericAmount);
  };

  return (
    <SafeAreaView style={tabunganStyles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}
      >
        <ScrollView contentContainerStyle={tabunganStyles.scrollContent}>
          {/* Form Container */}
          <View style={tabunganStyles.formContainer}>
            <Text style={tabunganStyles.formTitle}>💰 Tambah Setoran Baru</Text>

            {/* Amount Input */}
            <View style={tabunganStyles.inputContainer}>
              <Text style={tabunganStyles.label}>Jumlah Setoran *</Text>
              <TextInput
                style={[
                  tabunganStyles.input,
                  focusedInput === 'amount' && tabunganStyles.inputFocused,
                ]}
                value={amount}
                onChangeText={handleAmountChange}
                placeholder="Masukkan jumlah setoran"
                keyboardType="numeric"
                onFocus={() => setFocusedInput('amount')}
                onBlur={() => setFocusedInput(null)}
              />
              {amount && (
                <Text style={{ fontSize: 14, color: '#666', marginTop: 4 }}>
                  {formatDisplayCurrency(getNumericAmount())}
                </Text>
              )}
            </View>

            {/* Date Input */}
            <View style={tabunganStyles.inputContainer}>
              <Text style={tabunganStyles.label}>Tanggal Setoran *</Text>
              <TextInput
                style={[
                  tabunganStyles.input,
                  focusedInput === 'date' && tabunganStyles.inputFocused,
                ]}
                value={date}
                onChangeText={setDate}
                placeholder="YYYY-MM-DD"
                onFocus={() => setFocusedInput('date')}
                onBlur={() => setFocusedInput(null)}
              />
            </View>

            {/* Note Input */}
            <View style={tabunganStyles.inputContainer}>
              <Text style={tabunganStyles.label}>Catatan (Opsional)</Text>
              <TextInput
                style={[
                  tabunganStyles.input,
                  focusedInput === 'note' && tabunganStyles.inputFocused,
                  { height: 80, textAlignVertical: 'top' },
                ]}
                value={note}
                onChangeText={setNote}
                placeholder="Tambahkan catatan untuk setoran ini..."
                multiline
                numberOfLines={3}
                onFocus={() => setFocusedInput('note')}
                onBlur={() => setFocusedInput(null)}
              />
            </View>

            {/* Submit Button */}
            <TouchableOpacity
              style={[
                tabunganStyles.button,
                loading && tabunganStyles.buttonDisabled,
              ]}
              onPress={handleSubmit}
              disabled={loading}
            >
              <Text style={tabunganStyles.buttonText}>
                {loading ? 'Menyimpan...' : 'Simpan Setoran'}
              </Text>
            </TouchableOpacity>
          </View>

          {/* Info Card */}
          <View style={tabunganStyles.infoCard}>
            <View style={tabunganStyles.infoIcon}>
              <Ionicons name="information-circle" size={24} color="#2196F3" />
            </View>
            <Text style={tabunganStyles.infoText}>
              Pastikan jumlah dan tanggal setoran sudah benar sebelum menyimpan. 
              Data yang sudah disimpan tidak dapat diubah.
            </Text>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}
