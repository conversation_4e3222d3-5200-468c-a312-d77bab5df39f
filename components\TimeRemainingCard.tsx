import { Ionicons } from '@expo/vector-icons';
import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { BorderRadius, Colors, Spacing } from '../constants/theme';
import { getTypographyWithWeight } from '../utils/designSystem';

interface TimeRemainingCardProps {
  monthsRemaining: number;
  style?: any;
}

export const TimeRemainingCard: React.FC<TimeRemainingCardProps> = ({
  monthsRemaining,
  style
}) => {
  return (
    <View
      style={[styles.container, style]}
      accessibilityRole="text"
      accessibilityLabel={`Waktu tersisa ${monthsRemaining} bulan lagi untuk mencapai target tabungan haji`}
      accessible={true}
    >
      <View style={styles.iconContainer}>
        <Ionicons 
          name="calendar" 
          size={24} 
          color={Colors.base} 
        />
      </View>
      
      <View style={styles.textContainer}>
        <Text style={styles.valueText}>{monthsRemaining}</Text>
        <Text style={styles.labelText}>Bulan Lagi</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.neutral_light,
    borderRadius: BorderRadius.pill,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    minHeight: 48, // Accessibility touch target
    gap: Spacing.sm,
    alignSelf: 'flex-start', // Don't stretch full width
  },
  
  iconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.background,
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  textContainer: {
    alignItems: 'center',
    gap: 2,
  },
  
  valueText: {
    ...getTypographyWithWeight('h2', 'bold'),
    color: Colors.base,
  },
  
  labelText: {
    ...getTypographyWithWeight('caption', 'medium'),
    color: Colors.text_secondary,
  },
});

export default TimeRemainingCard;
