import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { router } from 'expo-router';
import React from 'react';
import { Dimensions, SafeAreaView, ScrollView, Text, TouchableOpacity, View } from 'react-native';
import { useAuth } from '../../contexts/AuthContext';
import { homeScreenStyles } from './styles';

const { width } = Dimensions.get('window');

export default function HomeScreen() {
  const { user, signOut } = useAuth();

  // Mock data - in real app, this would come from database
  const savingsData = {
    currentAmount: 2500000,
    targetAmount: 10000000,
    selectedAgent: 'Ibu Sari',
    agentPhone: '0812-3456-7890'
  };

  const progressPercentage = Math.round((savingsData.currentAmount / savingsData.targetAmount) * 100);

  const handleSignOut = async () => {
    await signOut();
    router.replace('/(auth)/login');
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(amount);
  };



  return (
    <SafeAreaView style={homeScreenStyles.container}>
      <ScrollView
        contentContainerStyle={homeScreenStyles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Header with Gradient */}
        <LinearGradient
          colors={['#1976D2', '#1565C0', '#0D47A1']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={homeScreenStyles.headerGradient}
        >
          <View style={homeScreenStyles.header}>
            <View style={homeScreenStyles.headerLeft}>
              <Text style={homeScreenStyles.appTitle}>💰 SaveMoney</Text>
              <Text style={homeScreenStyles.headerSubtitle}>Kelola tabungan dengan mudah</Text>
            </View>
            <TouchableOpacity style={homeScreenStyles.signOutButton} onPress={handleSignOut}>
              <Ionicons name="log-out-outline" size={22} color="#fff" />
            </TouchableOpacity>
          </View>
        </LinearGradient>

        {/* Welcome Card */}
        <View style={homeScreenStyles.welcomeCard}>
          <View style={homeScreenStyles.welcomeHeader}>
            <View style={homeScreenStyles.avatarContainer}>
              <Ionicons name="person-circle" size={50} color="#1976D2" />
            </View>
            <View style={homeScreenStyles.welcomeInfo}>
              <Text style={homeScreenStyles.welcomeText}>Selamat Datang!</Text>
              <Text style={homeScreenStyles.userEmail}>{user?.email}</Text>
            </View>
          </View>
        </View>

        {/* Main Progress Card */}
        <View style={homeScreenStyles.mainProgressCard}>
          <LinearGradient
            colors={['#E3F2FD', '#BBDEFB']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={homeScreenStyles.progressGradient}
          >
            {/* Left Side - Amount Info */}
            <View style={homeScreenStyles.progressLeft}>
              <View style={homeScreenStyles.progressHeader}>
                <View style={homeScreenStyles.walletIconContainer}>
                  <Ionicons name="wallet" size={24} color="#1976D2" />
                </View>
                <Text style={homeScreenStyles.progressTitle}>Progress Tabungan</Text>
              </View>

              <View style={homeScreenStyles.amountDisplay}>
                <Text style={homeScreenStyles.currentAmount}>{formatCurrency(savingsData.currentAmount)}</Text>
                <Text style={homeScreenStyles.targetAmountLabel}>dari {formatCurrency(savingsData.targetAmount)}</Text>
              </View>
            </View>

            {/* Right Side - Progress Info */}
            <View style={homeScreenStyles.progressRight}>
              <View style={homeScreenStyles.progressBadge}>
                <LinearGradient
                  colors={['#1976D2', '#0D47A1']}
                  style={homeScreenStyles.progressBadgeGradient}
                >
                  <Text style={homeScreenStyles.progressText}>{progressPercentage}%</Text>
                </LinearGradient>
              </View>

              <View style={homeScreenStyles.progressBarContainer}>
                <View style={homeScreenStyles.progressBar}>
                  <LinearGradient
                    colors={['#1976D2', '#1565C0']}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 0 }}
                    style={[
                      homeScreenStyles.progressFill,
                      { width: `${Math.min(progressPercentage, 100)}%` }
                    ]}
                  />
                </View>
              </View>
            </View>
          </LinearGradient>
        </View>

        {/* Savings Stats Cards */}
        <View style={homeScreenStyles.statsSection}>
          <View style={homeScreenStyles.statsGrid}>
            <View style={[homeScreenStyles.statCard, homeScreenStyles.statCard1]}>
              <LinearGradient
                colors={['#1976D2', '#1565C0']}
                style={homeScreenStyles.statGradient}
              >
                <Ionicons name="trending-up" size={26} color="#fff" />
                <Text style={homeScreenStyles.statValue}>{progressPercentage}%</Text>
                <Text style={homeScreenStyles.statLabel}>Progress</Text>
              </LinearGradient>
            </View>

            <View style={[homeScreenStyles.statCard, homeScreenStyles.statCard2]}>
              <LinearGradient
                colors={['#0D47A1', '#1565C0']}
                style={homeScreenStyles.statGradient}
              >
                <Ionicons name="calendar" size={26} color="#fff" />
                <Text style={homeScreenStyles.statValue}>8</Text>
                <Text style={homeScreenStyles.statLabel}>Bulan Lagi</Text>
              </LinearGradient>
            </View>
          </View>
        </View>



        {/* Agent Card */}
        <View style={homeScreenStyles.agentSection}>
          <Text style={homeScreenStyles.sectionTitle}>Agent Anda</Text>
          <View style={homeScreenStyles.agentCard}>
            <LinearGradient
              colors={['#E3F2FD', '#BBDEFB']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
              style={homeScreenStyles.agentGradient}
            >
              <View style={homeScreenStyles.agentIcon}>
                <Ionicons name="person" size={28} color="#fff" />
                <View style={homeScreenStyles.agentBadge}>
                  <Ionicons name="checkmark" size={10} color="#fff" />
                </View>
              </View>
              <View style={homeScreenStyles.agentInfo}>
                <Text style={homeScreenStyles.agentName}>{savingsData.selectedAgent}</Text>
                <View style={homeScreenStyles.agentStatus}>
                  <View style={homeScreenStyles.statusDot} />
                  <Text style={homeScreenStyles.statusText}>Siap Membantu</Text>
                </View>
                <Text style={homeScreenStyles.agentExperience}>Agent Terpercaya</Text>
              </View>
              <TouchableOpacity style={homeScreenStyles.callButton}>
                <LinearGradient
                  colors={['#1976D2', '#1565C0']}
                  style={homeScreenStyles.callButtonGradient}
                >
                  <Ionicons name="call" size={20} color="#fff" />
                </LinearGradient>
              </TouchableOpacity>
            </LinearGradient>
          </View>
        </View>


      </ScrollView>
    </SafeAreaView>
  );
}