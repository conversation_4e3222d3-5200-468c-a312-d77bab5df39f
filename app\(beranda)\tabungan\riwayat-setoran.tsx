import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React, { useState, useEffect } from 'react';
import {
  SafeAreaView,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
  RefreshControl,
} from 'react-native';
import { tabunganStyles } from './styles';

interface Setoran {
  id: string;
  amount: number;
  date: string;
  note?: string;
  createdAt: string;
}

export default function RiwayatSetoranScreen() {
  const [setoranList, setSetoranList] = useState<Setoran[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Mock data - in real app, this would come from database
  const mockData: Setoran[] = [
    {
      id: '1',
      amount: 500000,
      date: '2024-01-15',
      note: 'Setoran rutin bulanan',
      createdAt: '2024-01-15T10:30:00Z',
    },
    {
      id: '2',
      amount: 250000,
      date: '2024-01-08',
      note: 'Bonus dari peker<PERSON>an',
      createdAt: '2024-01-08T14:20:00Z',
    },
    {
      id: '3',
      amount: 100000,
      date: '2024-01-01',
      note: 'Setoran awal tahun',
      createdAt: '2024-01-01T09:00:00Z',
    },
    {
      id: '4',
      amount: 300000,
      date: '2023-12-25',
      createdAt: '2023-12-25T16:45:00Z',
    },
    {
      id: '5',
      amount: 150000,
      date: '2023-12-15',
      note: 'Setoran tambahan',
      createdAt: '2023-12-15T11:15:00Z',
    },
  ];

  const loadData = async () => {
    try {
      // TODO: Load from database
      // For now, use mock data
      await new Promise(resolve => setTimeout(resolve, 1000));
      setSetoranList(mockData);
    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  useEffect(() => {
    loadData();
  }, []);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('id-ID', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const getTotalAmount = () => {
    return setoranList.reduce((total, setoran) => total + setoran.amount, 0);
  };

  const renderEmptyState = () => (
    <View style={tabunganStyles.emptyState}>
      <View style={tabunganStyles.emptyStateIcon}>
        <Ionicons name="wallet-outline" size={64} color="#ccc" />
      </View>
      <Text style={tabunganStyles.emptyStateText}>
        Belum ada riwayat setoran.{'\n'}
        Mulai menabung dengan menambah setoran pertama Anda!
      </Text>
      <TouchableOpacity
        style={[tabunganStyles.button, { marginTop: 20 }]}
        onPress={() => router.push('/(beranda)/tabungan/tambah-setoran')}
      >
        <Text style={tabunganStyles.buttonText}>Tambah Setoran</Text>
      </TouchableOpacity>
    </View>
  );

  const renderSetoranItem = (setoran: Setoran) => (
    <View key={setoran.id} style={tabunganStyles.listItem}>
      <View style={tabunganStyles.listItemHeader}>
        <Text style={tabunganStyles.listItemDate}>
          {formatDate(setoran.date)}
        </Text>
        <Text style={tabunganStyles.listItemAmount}>
          {formatCurrency(setoran.amount)}
        </Text>
      </View>
      {setoran.note && (
        <Text style={tabunganStyles.listItemNote}>
          "{setoran.note}"
        </Text>
      )}
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={tabunganStyles.container}>
        <View style={[tabunganStyles.emptyState, { justifyContent: 'center' }]}>
          <Ionicons name="hourglass-outline" size={48} color="#ccc" />
          <Text style={[tabunganStyles.emptyStateText, { marginTop: 16 }]}>
            Memuat riwayat setoran...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={tabunganStyles.container}>
      <ScrollView
        contentContainerStyle={tabunganStyles.scrollContent}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Summary Card */}
        <View style={tabunganStyles.currencyContainer}>
          <Text style={tabunganStyles.currencyLabel}>Total Setoran</Text>
          <Text style={tabunganStyles.currencyAmount}>
            {formatCurrency(getTotalAmount())}
          </Text>
          <Text style={{ fontSize: 14, color: '#666', marginTop: 4 }}>
            Dari {setoranList.length} kali setoran
          </Text>
        </View>

        {/* List Container */}
        {setoranList.length === 0 ? (
          renderEmptyState()
        ) : (
          <View style={tabunganStyles.listContainer}>
            {setoranList.map(renderSetoranItem)}
          </View>
        )}

        {/* Add Button */}
        {setoranList.length > 0 && (
          <TouchableOpacity
            style={[tabunganStyles.button, { marginTop: 20 }]}
            onPress={() => router.push('/(beranda)/tabungan/tambah-setoran')}
          >
            <Text style={tabunganStyles.buttonText}>Tambah Setoran Baru</Text>
          </TouchableOpacity>
        )}

        {/* Info Card */}
        <View style={[tabunganStyles.infoCard, { marginTop: 20 }]}>
          <View style={tabunganStyles.infoIcon}>
            <Ionicons name="information-circle" size={24} color="#2196F3" />
          </View>
          <Text style={tabunganStyles.infoText}>
            Tarik ke bawah untuk memperbarui data. 
            Riwayat setoran diurutkan dari yang terbaru.
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
