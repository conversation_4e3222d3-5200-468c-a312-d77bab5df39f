import { Stack } from 'expo-router';
import React from 'react';

export default function TabunganLayout() {
  return (
    <Stack>
      <Stack.Screen 
        name="index" 
        options={{ 
          title: 'Menu Tabungan',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="tambah-setoran" 
        options={{ 
          title: 'Tambah Setoran',
          headerShown: true,
          headerStyle: {
            backgroundColor: '#2E7D32',
          },
          headerTintColor: '#fff',
          headerTitleStyle: {
            fontSize: 20,
            fontWeight: 'bold',
          },
        }} 
      />
      <Stack.Screen 
        name="riwayat-setoran" 
        options={{ 
          title: 'Riwayat Setoran',
          headerShown: true,
          headerStyle: {
            backgroundColor: '#2E7D32',
          },
          headerTintColor: '#fff',
          headerTitleStyle: {
            fontSize: 20,
            fontWeight: 'bold',
          },
        }} 
      />
      <Stack.Screen 
        name="simulasi-target" 
        options={{ 
          title: 'Simulasi Target',
          headerShown: true,
          headerStyle: {
            backgroundColor: '#2E7D32',
          },
          headerTintColor: '#fff',
          headerTitleStyle: {
            fontSize: 20,
            fontWeight: 'bold',
          },
        }} 
      />
    </Stack>
  );
}
