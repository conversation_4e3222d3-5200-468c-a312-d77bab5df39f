import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React from 'react';
import {
  SafeAreaView,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { tabunganStyles } from './styles';

export default function TabunganMenuScreen() {
  const menuItems = [
    {
      id: 'tambah-setoran',
      title: 'Tambah Setoran Baru',
      subtitle: 'Catat setoran tabungan Anda',
      icon: 'add-circle' as const,
      color: '#4CAF50',
      route: '/(beranda)/tabungan/tambah-setoran' as const,
    },
    {
      id: 'riwayat-setoran',
      title: 'Riwayat Setoran',
      subtitle: 'Lihat semua setoran yang sudah dibuat',
      icon: 'list' as const,
      color: '#2196F3',
      route: '/(beranda)/tabungan/riwayat-setoran' as const,
    },
    {
      id: 'simulasi-target',
      title: 'Simulasi Target',
      subtitle: 'Hitung berapa lagi yang perlu ditabung',
      icon: 'calculator' as const,
      color: '#FF9800',
      route: '/(beranda)/tabungan/simulasi-target' as const,
    },
  ];

  const handleMenuPress = (route: string) => {
    router.push(route as any);
  };

  return (
    <SafeAreaView style={tabunganStyles.container}>
      <ScrollView contentContainerStyle={tabunganStyles.scrollContent}>
        {/* Header */}
        <View style={tabunganStyles.header}>
          <Text style={tabunganStyles.headerTitle}>💰 Menu Tabungan</Text>
          <Text style={tabunganStyles.headerSubtitle}>
            Kelola tabungan Anda dengan mudah
          </Text>
        </View>

        {/* Menu Items */}
        <View style={tabunganStyles.menuContainer}>
          {menuItems.map((item) => (
            <TouchableOpacity
              key={item.id}
              style={tabunganStyles.menuItem}
              onPress={() => handleMenuPress(item.route)}
              activeOpacity={0.7}
              accessibilityRole="button"
              accessibilityLabel={`${item.title}. ${item.subtitle}`}
              accessibilityHint="Ketuk untuk membuka menu ini"
            >
              <View style={[tabunganStyles.menuIcon, { backgroundColor: item.color }]}>
                <Ionicons name={item.icon} size={32} color="#fff" />
              </View>
              <View style={tabunganStyles.menuContent}>
                <Text style={tabunganStyles.menuTitle}>{item.title}</Text>
                <Text style={tabunganStyles.menuSubtitle}>{item.subtitle}</Text>
              </View>
              <View style={tabunganStyles.menuArrow}>
                <Ionicons name="chevron-forward" size={24} color="#ccc" />
              </View>
            </TouchableOpacity>
          ))}
        </View>

        {/* Info Card */}
        <View style={tabunganStyles.infoCard}>
          <View style={tabunganStyles.infoIcon}>
            <Ionicons name="information-circle" size={24} color="#2196F3" />
          </View>
          <Text style={tabunganStyles.infoText}>
            Gunakan menu di atas untuk mengelola tabungan Anda. 
            Semua data akan tersimpan dengan aman.
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
