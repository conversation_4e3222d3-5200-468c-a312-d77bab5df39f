import { Ionicons } from '@expo/vector-icons';
import React, { useState } from 'react';
import {
  SafeAreaView,
  ScrollView,
  Text,
  TextInput,
  View
} from 'react-native';
import { tabunganStyles } from './styles';

export default function SimulasiTargetScreen() {
  const [targetAmount, setTargetAmount] = useState('');
  const [monthlyDeposit, setMonthlyDeposit] = useState('');
  const [focusedInput, setFocusedInput] = useState<string | null>(null);

  // Mock current savings - in real app, this would come from database
  const currentSavings = 2500000;

  const formatCurrency = (value: string) => {
    const numericValue = value.replace(/[^0-9]/g, '');
    if (numericValue) {
      return new Intl.NumberFormat('id-ID').format(parseInt(numericValue));
    }
    return '';
  };

  const handleTargetAmountChange = (text: string) => {
    const formatted = formatCurrency(text);
    setTargetAmount(formatted);
  };

  const handleMonthlyDepositChange = (text: string) => {
    const formatted = formatCurrency(text);
    setMonthlyDeposit(formatted);
  };

  const getNumericValue = (formattedValue: string) => {
    return parseInt(formattedValue.replace(/[^0-9]/g, '')) || 0;
  };

  const calculateSimulation = () => {
    const target = getNumericValue(targetAmount);
    const monthly = getNumericValue(monthlyDeposit);
    
    if (target <= 0 || monthly <= 0) return null;

    const remaining = target - currentSavings;
    if (remaining <= 0) {
      return {
        remaining: 0,
        months: 0,
        years: 0,
        remainingMonths: 0,
        isTargetReached: true,
      };
    }

    const months = Math.ceil(remaining / monthly);
    const years = Math.floor(months / 12);
    const remainingMonths = months % 12;

    return {
      remaining,
      months,
      years,
      remainingMonths,
      isTargetReached: false,
    };
  };

  const formatDisplayCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const getProgressPercentage = () => {
    const target = getNumericValue(targetAmount);
    if (target <= 0) return 0;
    return Math.min((currentSavings / target) * 100, 100);
  };

  const simulation = calculateSimulation();

  return (
    <SafeAreaView style={tabunganStyles.container}>
      <ScrollView contentContainerStyle={tabunganStyles.scrollContent}>
        {/* Current Savings Display */}
        <View style={tabunganStyles.currencyContainer}>
          <Text style={tabunganStyles.currencyLabel}>Tabungan Saat Ini</Text>
          <Text style={tabunganStyles.currencyAmount}>
            {formatDisplayCurrency(currentSavings)}
          </Text>
        </View>

        {/* Input Form */}
        <View style={tabunganStyles.formContainer}>
          <Text style={tabunganStyles.formTitle}>🎯 Simulasi Target</Text>

          {/* Target Amount Input */}
          <View style={tabunganStyles.inputContainer}>
            <Text style={tabunganStyles.label}>Target Tabungan *</Text>
            <TextInput
              style={[
                tabunganStyles.input,
                focusedInput === 'target' && tabunganStyles.inputFocused,
              ]}
              value={targetAmount}
              onChangeText={handleTargetAmountChange}
              placeholder="Masukkan target tabungan"
              keyboardType="numeric"
              onFocus={() => setFocusedInput('target')}
              onBlur={() => setFocusedInput(null)}
            />
            {targetAmount && (
              <Text style={{ fontSize: 14, color: '#666', marginTop: 4 }}>
                {formatDisplayCurrency(getNumericValue(targetAmount))}
              </Text>
            )}
          </View>

          {/* Monthly Deposit Input */}
          <View style={tabunganStyles.inputContainer}>
            <Text style={tabunganStyles.label}>Setoran Bulanan *</Text>
            <TextInput
              style={[
                tabunganStyles.input,
                focusedInput === 'monthly' && tabunganStyles.inputFocused,
              ]}
              value={monthlyDeposit}
              onChangeText={handleMonthlyDepositChange}
              placeholder="Masukkan setoran per bulan"
              keyboardType="numeric"
              onFocus={() => setFocusedInput('monthly')}
              onBlur={() => setFocusedInput(null)}
            />
            {monthlyDeposit && (
              <Text style={{ fontSize: 14, color: '#666', marginTop: 4 }}>
                {formatDisplayCurrency(getNumericValue(monthlyDeposit))} per bulan
              </Text>
            )}
          </View>
        </View>

        {/* Progress Bar */}
        {targetAmount && (
          <View style={tabunganStyles.formContainer}>
            <Text style={[tabunganStyles.formTitle, { fontSize: 18 }]}>
              Progress Saat Ini
            </Text>
            <View style={{
              backgroundColor: '#E0E0E0',
              height: 20,
              borderRadius: 10,
              marginBottom: 10,
            }}>
              <View style={{
                backgroundColor: '#4CAF50',
                height: 20,
                borderRadius: 10,
                width: `${getProgressPercentage()}%`,
              }} />
            </View>
            <Text style={{
              textAlign: 'center',
              fontSize: 16,
              fontWeight: 'bold',
              color: '#2E7D32',
            }}>
              {getProgressPercentage().toFixed(1)}% tercapai
            </Text>
          </View>
        )}

        {/* Simulation Results */}
        {simulation && (
          <View style={tabunganStyles.formContainer}>
            <Text style={[tabunganStyles.formTitle, { fontSize: 18 }]}>
              Hasil Simulasi
            </Text>

            {simulation.isTargetReached ? (
              <View style={{ alignItems: 'center', padding: 20 }}>
                <Ionicons name="checkmark-circle" size={64} color="#4CAF50" />
                <Text style={{
                  fontSize: 20,
                  fontWeight: 'bold',
                  color: '#4CAF50',
                  textAlign: 'center',
                  marginTop: 16,
                }}>
                  🎉 Selamat!
                </Text>
                <Text style={{
                  fontSize: 16,
                  color: '#666',
                  textAlign: 'center',
                  marginTop: 8,
                }}>
                  Target tabungan Anda sudah tercapai!
                </Text>
              </View>
            ) : (
              <>
                {/* Remaining Amount */}
                <View style={{
                  backgroundColor: '#FFF3E0',
                  padding: 16,
                  borderRadius: 12,
                  marginBottom: 16,
                }}>
                  <Text style={{
                    fontSize: 14,
                    color: '#F57C00',
                    marginBottom: 4,
                  }}>
                    Sisa yang perlu ditabung:
                  </Text>
                  <Text style={{
                    fontSize: 24,
                    fontWeight: 'bold',
                    color: '#F57C00',
                  }}>
                    {formatDisplayCurrency(simulation.remaining)}
                  </Text>
                </View>

                {/* Time Needed */}
                <View style={{
                  backgroundColor: '#E8F5E8',
                  padding: 16,
                  borderRadius: 12,
                  marginBottom: 16,
                }}>
                  <Text style={{
                    fontSize: 14,
                    color: '#2E7D32',
                    marginBottom: 4,
                  }}>
                    Waktu yang dibutuhkan:
                  </Text>
                  <Text style={{
                    fontSize: 20,
                    fontWeight: 'bold',
                    color: '#2E7D32',
                  }}>
                    {simulation.years > 0 && `${simulation.years} tahun `}
                    {simulation.remainingMonths > 0 && `${simulation.remainingMonths} bulan`}
                    {simulation.years === 0 && simulation.remainingMonths === 0 && `${simulation.months} bulan`}
                  </Text>
                  <Text style={{
                    fontSize: 14,
                    color: '#666',
                    marginTop: 4,
                  }}>
                    (Total: {simulation.months} bulan)
                  </Text>
                </View>

                {/* Monthly Breakdown */}
                <View style={{
                  backgroundColor: '#F3E5F5',
                  padding: 16,
                  borderRadius: 12,
                }}>
                  <Text style={{
                    fontSize: 14,
                    color: '#7B1FA2',
                    marginBottom: 4,
                  }}>
                    Dengan setoran bulanan:
                  </Text>
                  <Text style={{
                    fontSize: 18,
                    fontWeight: 'bold',
                    color: '#7B1FA2',
                  }}>
                    {formatDisplayCurrency(getNumericValue(monthlyDeposit))}
                  </Text>
                </View>
              </>
            )}
          </View>
        )}

        {/* Info Card */}
        <View style={tabunganStyles.infoCard}>
          <View style={tabunganStyles.infoIcon}>
            <Ionicons name="information-circle" size={24} color="#2196F3" />
          </View>
          <Text style={tabunganStyles.infoText}>
            Simulasi ini membantu Anda merencanakan tabungan. 
            Masukkan target dan jumlah setoran bulanan untuk melihat berapa lama waktu yang dibutuhkan.
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
